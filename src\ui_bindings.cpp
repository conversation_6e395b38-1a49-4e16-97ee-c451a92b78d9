// ui_bindings.cpp
#include "bindings.h"
#include "ui.h" // tes objets générés (ex: 'objects' ou accès à tes boutons)

extern objects_t objects; // si ton générateur expose cette struct

void register_ui_bindings() {
  // Récupère tes objets créés par le générateur et enregistre-les.
  // Exemple basé sur ton snippet: bouton à (110,95) avec un label enfant
  lv_obj_t* btn1 = /* retrouve le bouton 1 (via un getter, un pointeur global du générateur, ou lv_obj_get_child) */;
  lv_obj_t* lbl1 = lv_obj_get_child(btn1, 0);

  BtnBinding binding;
  binding.btn = btn1;
  binding.label = lbl1;
  binding.topicSet = "home/panel1/button1/set";
  binding.topicState = "home/panel1/button1/state";
  uiBindings.buttons.push_back(binding);

  // Répète pour d’autres widgets si besoin
}
