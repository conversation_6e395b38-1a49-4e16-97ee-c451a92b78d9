#pragma once
#include <Arduino.h>

// Prototype principal (avec valeurs par défaut)
void mqtt_publish_set(const String& topic, const char* payload, uint8_t qos = 0, bool retain = false);

// Surcharge pratique 2-arguments
inline void mqtt_publish_set(const String& topic, const char* payload) {
  mqtt_publish_set(topic, payload, 0, false);
}

// (le reste)
bool mqtt_ready();
void mqtt_publish_button_pressed(uint8_t btnIndex);
