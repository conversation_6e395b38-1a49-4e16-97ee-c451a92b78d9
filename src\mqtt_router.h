#pragma once
#include <AsyncMqttClient.h>
#include <vector>
#include <functional>

using MqttHandler = std::function<void(const char* topic, const char* payload, size_t len)>;

class MqttRouter {
public:
  void begin(AsyncMqttClient& c) {
    client = &c;
    client->onMessage([this](char* topic, char* payload, AsyncMqttClientMessageProperties, size_t len, size_t, size_t){
      for (auto &s : subs) if (s.topic == topic) s.cb(topic, payload, len);
    });
  }
  void subscribe(const String& topic, MqttHandler cb, uint8_t qos = 0) {
    subs.push_back({topic, std::move(cb)});
    if (client) client->subscribe(topic.c_str(), qos);
  }
  void resubscribeAll(uint8_t qos = 0) {
    if (!client) return;
    for (auto &s : subs) client->subscribe(s.topic.c_str(), qos);
  }
private:
  struct Sub { String topic; MqttHandler cb; };
  AsyncMqttClient* client = nullptr;
  std::vector<Sub> subs;
};
