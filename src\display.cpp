#include <lvgl.h>
#include <TFT_eSPI.h>
#include "touch.h"

extern TFT_eSPI tft;  // Déclaré dans touch.cpp ou globalement

void setup_lvgl() {
    lv_init();
    tft.begin();
    tft.setRotation(1);

    static lv_disp_draw_buf_t draw_buf;
    static lv_color_t buf[LV_HOR_RES_MAX * 10];
    lv_disp_draw_buf_init(&draw_buf, buf, NULL, LV_HOR_RES_MAX * 10);

    static lv_disp_drv_t disp_drv;
    lv_disp_drv_init(&disp_drv);
    disp_drv.flush_cb = [](lv_disp_drv_t *disp, const lv_area_t *area, lv_color_t *color_p) {
        SPI.beginTransaction(SPISettings(40000000, MSBFIRST, SPI_MODE0));
        tft.startWrite();
        tft.setAddrWindow(area->x1, area->y1,
                          area->x2 - area->x1 + 1,
                          area->y2 - area->y1 + 1);
        tft.pushColors((uint16_t *)&color_p->full,
                       (area->x2 - area->x1 + 1) * (area->y2 - area->y1 + 1),
                       true);
        tft.endWrite();
        SPI.endTransaction();
        lv_disp_flush_ready(disp);
    };
    disp_drv.draw_buf = &draw_buf;
    disp_drv.hor_res = 320;
    disp_drv.ver_res = 240;
    lv_disp_drv_register(&disp_drv);

    // Tactile
    register_touch_driver();
}
