; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
build_type = debug
lib_deps = 
	knolleary/PubSubClient@^2.8
	bodmer/TFT_eSPI@^2.5.41
	https://github.com/PaulStoffregen/XPT2046_Touchscreen.git#v1.4
	bblanchon/Arduino<PERSON><PERSON>@^6.21.2
	heman/AsyncMqttClient-esphome@^2.1.0
build_flags = 
	-D LV_CONF_INCLUDE_SIMPLE
	-Ilib/lvgl
	-Ilib/lv_conf
	-I src/ui
