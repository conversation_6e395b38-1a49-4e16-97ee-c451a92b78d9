#pragma once
#include <lvgl.h>
#include <Arduino.h>
#include "bindings.h"

inline void apply_button_state(BtnBinding& b, const char* payload, size_t len) {
  String s(payload, len); s.trim();
  if (s.equalsIgnoreCase("ON") || s.equalsIgnoreCase("pressed")) {
    lv_obj_add_state(b.btn, LV_STATE_CHECKED);
    if (b.label) lv_label_set_text(b.label, "ON");
  } else if (s.equalsIgnoreCase("OFF")) {
    lv_obj_clear_state(b.btn, LV_STATE_CHECKED);
    if (b.label) lv_label_set_text(b.label, "OFF");
  } else {
    if (b.label) lv_label_set_text(b.label, s.c_str());
  }
  if (b.label) lv_obj_center(b.label);
}
