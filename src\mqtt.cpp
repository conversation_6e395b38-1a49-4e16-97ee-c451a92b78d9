// mqtt.cpp
#include <WiFi.h>
#include <AsyncMqttClient.h>
#include "mqtt_router.h"
#include "bindings.h"
#include "apply_ui.h"
#include "lv_safe.h"

// === Réglages ===
static const char* WIFI_SSID = "Freebox-C2744E";
static const char* WIFI_PASS = "943xzq4s7tq3vnstq7mkk4";
static const char* MQTT_HOST = "************";
static const uint16_t MQTT_PORT = 1883;
static const char* MQTT_CLIENT_ID = "esp32_panel_1";
static const char* TOPIC_STATUS = "home/panel1/status";
static const char* MQTT_USER = "admin";
static const char* MQTT_PASS = "Undress2-Primary-Unbutton";

AsyncMqttClient mqttClient;
MqttRouter      mqttRouter;

static TimerHandle_t mqttReconnectTimer;
static TimerHandle_t wifiReconnectTimer;

static void connectToWifi(){ Serial.println("[WiFi] Connexion…"); WiFi.begin(WIFI_SSID, WIFI_PASS); }
static void connectToMqtt(){ Serial.println("[MQTT] Connexion…"); mqttClient.connect(); }

static void bind_all_topics() {
  for (auto &b : uiBindings.buttons) {
    mqttRouter.subscribe(b.topicState, [&b](const char*, const char* payload, size_t len){
      lv_post([&b, p = std::string(payload, payload + len)](){
        apply_button_state(b, p.c_str(), p.size());
      });
    });
  }
}

void mqtt_setup() {
  // Timers
  mqttReconnectTimer = xTimerCreate("mqttTimer", pdMS_TO_TICKS(2000), pdFALSE, (void*)0, [](TimerHandle_t){ connectToMqtt(); });
  wifiReconnectTimer = xTimerCreate("wifiTimer", pdMS_TO_TICKS(2000), pdFALSE, (void*)0, [](TimerHandle_t){ connectToWifi(); });

  // Wi-Fi events
  WiFi.onEvent([](WiFiEvent_t event){
    switch (event) {
      case ARDUINO_EVENT_WIFI_STA_GOT_IP:
        Serial.printf("[WiFi] IP: %s\n", WiFi.localIP().toString().c_str());
        connectToMqtt();
        break;
      case ARDUINO_EVENT_WIFI_STA_DISCONNECTED:
        Serial.println("[WiFi] Perdu. Reconnexion…");
        xTimerStop(mqttReconnectTimer, 0);
        xTimerStart(wifiReconnectTimer, 0);
        break;
      default: break;
    }
  });

  // MQTT config
  mqttClient.setClientId(MQTT_CLIENT_ID);
  mqttClient.setServer(MQTT_HOST, MQTT_PORT);
  mqttClient.setCredentials(MQTT_USER, MQTT_PASS);
  mqttClient.setWill(TOPIC_STATUS, 0, true, "offline");

  mqttClient.onConnect([](bool){
    Serial.println("[MQTT] Connecté");
    mqttClient.publish(TOPIC_STATUS, 0, true, "online");
    mqttRouter.resubscribeAll(0);
  });
  mqttClient.onDisconnect([](AsyncMqttClientDisconnectReason reason){
    Serial.printf("[MQTT] Déconnecté: %d\n", (int)reason);
    if (WiFi.isConnected()) xTimerStart(mqttReconnectTimer, 0);
  });

  mqttRouter.begin(mqttClient);

  // IMPORTANT : à ce stade, uiBindings doit déjà être rempli
  bind_all_topics();

  // Démarre le Wi-Fi
  connectToWifi();
}

void mqtt_publish_set(const String& topic, const char* payload, uint8_t qos, bool retain) {
  if (!mqttClient.connected()) {
    Serial.println("[MQTT] publish ignoré (offline)");
    return;
  }
  mqttClient.publish(topic.c_str(), qos, retain, payload);
}

bool mqtt_ready() { return mqttClient.connected(); }

void mqtt_publish_button_pressed(uint8_t btnIndex) {
  if (btnIndex == 0 || btnIndex > uiBindings.buttons.size()) return;
  auto &b = uiBindings.buttons[btnIndex - 1];
  // Grâce à la surcharge 2-args, ceci compile partout :
  mqtt_publish_set(b.topicSet, "pressed");
}
